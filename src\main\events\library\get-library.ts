import type { LibraryGame, UserProfile } from "@types";
import { registerEvent } from "../register-event";
import {
  downloadsSublevel,
  gamesShopAssetsSublevel,
  gamesSublevel,
} from "@main/level";
import { HydraApi } from "@main/services";
import { getUserData } from "@main/services/user/get-user-data";

const getLibrary = async (): Promise<LibraryGame[]> => {
  try {
    // First try to get user profile data with complete assets
    const userDetails = await getUserData();
    if (userDetails?.id) {
      const userProfile = await HydraApi.get<UserProfile>(`/users/${userDetails.id}`);
      if (userProfile?.libraryGames) {
        // Convert UserGame[] to LibraryGame[] with download info
        const libraryGamesWithDownloads = await Promise.all(
          userProfile.libraryGames.map(async (userGame) => {
            const key = `${userGame.shop}:${userGame.objectId}`;
            const download = await downloadsSublevel.get(key).catch(() => null);

            // Get local game data to merge with remote data
            const localGame = await gamesSublevel.get(key).catch(() => null);

            return {
              id: key,
              objectId: userGame.objectId,
              title: userGame.title,
              shop: userGame.shop,
              // Copy all ShopAssets properties
              coverImageUrl: userGame.coverImageUrl,
              libraryImageUrl: userGame.libraryImageUrl,
              libraryHeroImageUrl: userGame.libraryHeroImageUrl,
              logoImageUrl: userGame.logoImageUrl,
              iconUrl: userGame.iconUrl,
              logoPosition: userGame.logoPosition,
              // Add other Game properties - merge local and remote data
              isDeleted: false,
              executablePath: localGame?.executablePath || "",
              launchOptions: localGame?.launchOptions || "",
              winePrefixPath: localGame?.winePrefixPath || "",
              // Include favorite status and other user-specific data
              favorite: userGame.isFavorite || false,
              playTimeInMilliseconds: userGame.playTimeInSeconds ? userGame.playTimeInSeconds * 1000 : (localGame?.playTimeInMilliseconds || 0),
              lastTimePlayed: userGame.lastTimePlayed || localGame?.lastTimePlayed || null,
              remoteId: userGame.id || localGame?.remoteId || null,
              automaticCloudSync: localGame?.automaticCloudSync || false,
              download: download ?? null,
            } as LibraryGame;
          })
        );

        return libraryGamesWithDownloads;
      }
    }
  } catch (error) {
    console.warn("Failed to get library from user profile, falling back to local data:", error);
  }

  // Fallback to original local data approach
  return gamesSublevel
    .iterator()
    .all()
    .then((results) => {
      return Promise.all(
        results
          .filter(([_key, game]) => game.isDeleted === false)
          .map(async ([key, game]) => {
            const download = await downloadsSublevel.get(key);
            const gameAssets = await gamesShopAssetsSublevel.get(key);

            return {
              id: key,
              ...game,
              download: download ?? null,
              ...(gameAssets || {}),
            };
          })
      );
    });
};

registerEvent("getLibrary", getLibrary);
